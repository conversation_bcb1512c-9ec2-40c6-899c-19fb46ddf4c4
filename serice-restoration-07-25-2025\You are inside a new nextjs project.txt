You are inside a new nextjs project ok? it's inside <serice-restoration-07-25-2025>, you will have to cd into this directory to do things, i am on windows powershell so don't use && symbols

I want to create a High-Ranking SEO topical authority high tech directory called tech-hub-ireland.com - the idea behind this website is to create a high tech directory in Ireland

Service Restoration Site to Match 

Do all of these in order, the task is not completed until you’ve done all the steps in this list - continue operating until finished:

Use Next js 14.2.23 - these were my install settings:

Create icons and svgs as you’re going - start with something simple

Give ALL the entire folder and file structure as you see it at the beginning - including all files, all folders, in their respective directories, then give me the entire commands to create all folders and all files - I am on windows - you are inside a blank nextjs directory

Do not use src directory

When you give me code give me the file path in a comment at the top of the file

Step 0 - set up the file and folder structure by giving me all commands to create all files/folders in their respective directories - I am on windows

Step 1 - set up project with SEO (routing etc) in mind - ensure all pages are unique, have structured URLs programmatically, are their own URLs and not just filtered URLs - etc

Step 2 - Set up mongo caching and mongodb connection, as well as preparing the pixabay API

Step 3 - Set up dataforseo API and ensure it is working by sending test requests, receiving test responses, then changing your design to fit with the API response

Step 4 - Set up the homepage, and the index and subindex pages

Step 5 - Set up the individual business pages

Step 6 - Do the robots.txt, sitemap, meta titles, meta descriptions (all programmatically generated) - Use a recurring random list of 5 different meta title types, meta title descriptions, short description, with programmatically added elements to each. For example Best X in Y | Find a Y in X

Quiz Pages:

2. Create New Quiz Interface Component:
    - Build a clean quiz interface that displays all questions simultaneously
    - Implement numbered questions with clean multiple choice options
    - Add Scripture references for each question
    - Include comprehensive sidebar with 5 related categories/topics links
    - Add "Quick Study" section with additional topic links
    - Use minimal styling - no cards, shadows, or fancy elements
    - Ensure each has 16-20 comprehensive questions
    - Add proper SEO metadata and OpenGraph tags

5. Implement SEO Best Practices:
    - Proper heading hierarchy (H1, H2, H3)
    - Rich meta descriptions with keywords
    - Semantic HTML structure
    - Internal linking strategy via sidebar
    - Scripture verse references for authority

  6. Test Implementation:
    - Use browser automation to verify pages load correctly
    - Ensure quiz functionality works (question selection, results display)
    - Confirm sidebar links and navigation work properly

  Expected Outcomes:
  - All questions visible on single page (improves engagement time)
  - Comprehensive internal linking (builds site authority)
  - Clean, scannable design (improves user experience)
  - Rich content with explanations and verses (increases value)
  - Professional, minimal styling (reduces distractions)
  - Enhanced bottom section with 6 related posts in 3-column grid
  - Extensive internal linking throughout (15+ links per page)

  Key Success Metrics:
  - Longer time on page due to all questions being visible
  - Better internal link distribution for SEO
  - Higher content quality with verse references and explanations
  - Improved user experience with clean, focused design

  This format should significantly improve Google rankings by matching the structure of successful, high-ranking
  Bible quiz websites.

Be very wary of this error:

Error: ./

App Router and Pages Router both match path: /

Next.js does not support having both App Router and Pages Router routes matching the same path. Please remove one of the conflicting routes.

Home page - should have images from <pixabay documentation> and should be well laid out with striking hero images, modular individual icon design (use svgs to create icons) - For colors just use a very light green theme but mainly black and white, just with lime green as a highlight for certain backgrounds of modules - include revolving businesses in some way, and have 6 main index pages such as solar on the homepage

Index pages - a group of locations or types of business grouped together, where the individual cards shown to the arriving user are more refined types of businesses - for example an index page is “solar” and a sub index page is “solar farm installation”

sub index pages - these are individual niche types of businesses where the individual cards shown to the arriving user are individual businesses themselves, which also require a page of their own 

Individual business pages - these should be filled with information from the api - and should display reviews, and all other information you get from the api response from google - use the attached image to understand how and what to display

The business information will be supplied by the Dataforseo - Only 5 businesses should ever been searched for at a time, results should be cached in mongodb using these credentials:

mongodb+srv://hamishdavisonseo:<db_password>@cluster0.ulqwp.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0 

Name of collection on mongodb:

Techdirectoryireland

Use <dataforseodocumentation> to get a request, you can use <example response> to understand what the response will be, you can design an individual business page around this response, and cache the response in mongodb for 6 months

Think about SEO at every turn, meta titles like Review of X business in Y County of Ireland for individual business pages, meta titles like “Find a X in Y” for location + niche pages will rank well due to exact phrase matching

Create a sitemap using Next sitemap, create a robots.txt, create schema, and anything else relevant for every page

You should use programmatic SEO where you generate pags from the <countyofireland> + <techniches> to create a directory website programmatically

<dataforseodocumentation>

curl --location --request POST 'https://api.dataforseo.com/v3/serp/google/maps/live/advanced' \

--header 'Authorization: Basic ************************************************' \

--header 'Content-Type: application/json' \

--data-raw '[{"keyword":"solar panels sligo", "location_code":2372, "language_code":"en", "device":"desktop", "os":"windows", "depth":10}]'

</dataforseodocumentation

<techniches>

Pillar,Niche

Energy Assessment,BER Assessors

Energy Assessment,Home Energy Advisors

Energy Assessment,Retrofit Coordinators

Energy Assessment,Technical Surveyors

Solar,Solar PV Installation

Solar,Solar Battery Storage

Solar,Solar Hot Water Systems

Heating,Heat Pump Installation

Heating,Combi Boiler Replacement

Heating,Underfloor Heating Systems

Energy Efficiency,BER Assessment Services

Energy Efficiency,Attic Insulation

Energy Efficiency,Cavity Wall Insulation

Smart Home,Home Automation Systems

Smart Home,Smart Heating Controls

Smart Home,Smart Meter Installation

Electric,EV Home Charger Installation

Electric,Fuse Board Upgrades

Electric,Emergency Backup Systems

Network,Fiber Broadband Setup

Network,Mesh WiFi Installation

Network,Smart TV & Sound Systems

Plumbing,Combi Boiler Installation

Plumbing,Smart Leak Detection

Plumbing,Water Tank Replacement

Roofing,Slate Roof Repairs

Roofing,Roof Insulation

Roofing,Solar Panel Integration

Windows,Triple Glazing Installation

Windows,Window Replacement

Windows,Draught Proofing

Ventilation,Heat Recovery Systems

Ventilation,Humidity Control Systems

Ventilation,Attic Ventilation

Grant Services,SEAI Grant Applications

Grant Services,Home Energy Grants

Grant Services,Better Energy Homes

Energy Consultation,One-Stop-Shop Services

Energy Consultation,Retrofit Design Services

Energy Consultation,Energy Upgrade Planning

</techniches>

<countiesofireland>

Antrim	

Armagh	

Carlow[d]	

Cavan[d]	

Clare[d]	

Cork	

Donegal[d]	

Down	

Dublin	

	

	

	

Fermanagh	

Galway	

Kerry[d]	

Kildare[d]	

Kilkenny	

Laois[d]	

Leitrim[d]	

Limerick[d]	

Londonderry[b]	

Longford[d]	

Louth[d]	

Mayo	

Meath[d]	

Monaghan[d]	

Offaly[d]	

Roscommon[d]	

Sligo[d]	

Tipperary[d]	

Tyrone	

Waterford[d]	

Westmeath[d]	

Wexford[d]	

Wicklow[d]	

</countiesofireland>

<pixabay documentation>

Pixabay API

Welcome to the Pixabay API documentation. Our API is a RESTful interface for searching and retrieving royalty-free images and videos released by Pixabay under the Content License.

Free ImagesIf you make use of the API, show your users where the images and videos are from, whenever search results are displayed. That's the one thing we kindly request in return for free API usage.

The API returns JSON-encoded objects. Hash keys and values are case-sensitive and character encoding is in UTF-8. Hash keys may be returned in any random order and new keys may be added at any time. We will do our best to notify our users before removing hash keys from results or adding required parameters.

Rate Limit

By default, you can make up to 100 requests per 60 seconds. Requests are associated with the API key, and not with your IP address. The response headers tell you everything you need to know about your current rate limit status:

Header name	Description

X-RateLimit-Limit	The maximum number of requests that the consumer is permitted to make in 60 seconds.

X-RateLimit-Remaining	The number of requests remaining in the current rate limit window.

X-RateLimit-Reset	The remaining time in seconds after which the current rate limit window resets.

To keep the Pixabay API fast for everyone, requests must be cached for 24 hours. Also, the API is made for real human requests; do not send lots of automated queries. Systematic mass downloads are not allowed. If needed, we can increase this limit at any time - given that you've implemented the API properly.

Hotlinking

Returned image URLs may be used for temporarily displaying search results. However, permanent hotlinking of images (using Pixabay URLs in your app) is not allowed. If you intend to use the images, please download them to your server first. Videos may be embedded directly in your applications. Yet, we recommend storing them on your server.

Error Handling

If an error occurs, a response with propper HTTP error status code is returned. The body of this response contains a description of the issue in plain text. For example, once you go over the rate limit you will receive an HTTP error 429 ("Too Many Requests") with the message "API rate limit exceeded".

Search Images

GEThttps://pixabay.com/api/

Parameters

key (required)	str	Your API key: **********************************

q	str	A URL encoded search term. If omitted, all images are returned. This value may not exceed 100 characters.

Example: "yellow+flower"

lang	str	Language code of the language to be searched in.

Accepted values: cs, da, de, en, es, fr, id, it, hu, nl, no, pl, pt, ro, sk, fi, sv, tr, vi, th, bg, ru, el, ja, ko, zh

Default: "en"

id	str	Retrieve individual images by ID.

image_type	str	Filter results by image type.

Accepted values: "all", "photo", "illustration", "vector"

Default: "all"

orientation	str	Whether an image is wider than it is tall, or taller than it is wide.

Accepted values: "all", "horizontal", "vertical"

Default: "all"

category	str	Filter results by category.

Accepted values: backgrounds, fashion, nature, science, education, feelings, health, people, religion, places, animals, industry, computer, food, sports, transportation, travel, buildings, business, music

min_width	int	Minimum image width.

Default: "0"

min_height	int	Minimum image height.

Default: "0"

colors	str	Filter images by color properties. A comma separated list of values may be used to select multiple properties.

Accepted values: "grayscale", "transparent", "red", "orange", "yellow", "green", "turquoise", "blue", "lilac", "pink", "white", "gray", "black", "brown"

editors_choice	bool	Select images that have received an Editor's Choice award.

Accepted values: "true", "false"

Default: "false"

safesearch	bool	A flag indicating that only images suitable for all ages should be returned.

Accepted values: "true", "false"

Default: "false"

order	str	How the results should be ordered.

Accepted values: "popular", "latest"

Default: "popular"

page	int	Returned search results are paginated. Use this parameter to select the page number.

Default: 1

per_page	int	Determine the number of results per page.

Accepted values: 3 - 200

Default: 20

callback	string	JSONP callback function name

pretty	bool	Indent JSON output. This option should not be used in production.

Accepted values: "true", "false"

Default: "false"

Example

Retrieving photos of "yellow flowers". The search term q needs to be URL encoded:

https://pixabay.com/api/?key=**********************************&q=yellow+flowers&image_type=photo

Response for this request:

{

"total": 4692,

"totalHits": 500,

"hits": [

    {

        "id": 195893,

        "pageURL": "https://pixabay.com/en/blossom-bloom-flower-195893/",

        "type": "photo",

        "tags": "blossom, bloom, flower",

        "previewURL": "https://cdn.pixabay.com/photo/2013/10/15/09/12/flower-195893_150.jpg"

        "previewWidth": 150,

        "previewHeight": 84,

        "webformatURL": "https://pixabay.com/get/35bbf209e13e39d2_640.jpg",

        "webformatWidth": 640,

        "webformatHeight": 360,

        "largeImageURL": "https://pixabay.com/get/ed6a99fd0a76647_1280.jpg",

        "fullHDURL": "https://pixabay.com/get/ed6a9369fd0a76647_1920.jpg",

        "imageURL": "https://pixabay.com/get/ed6a9364a9fd0a76647.jpg",

        "imageWidth": 4000,

        "imageHeight": 2250,

        "imageSize": 4731420,

        "views": 7671,

        "downloads": 6439,

        "likes": 5,

        "comments": 2,

        "user_id": 48777,

        "user": "Josch13",

        "userImageURL": "https://cdn.pixabay.com/user/2013/11/05/02-10-23-764_250x250.jpg",

    },

    {

        "id": 73424,

        ...

    },

    ...

]

}

Response key	Description

total	The total number of hits.

totalHits	The number of images accessible through the API. By default, the API is limited to return a maximum of 500 images per query.

id	A unique identifier for this image.

pageURL	Source page on Pixabay, which provides a download link for the original image of the dimension imageWidth x imageHeight and the file size imageSize.

previewURL	Low resolution images with a maximum width or height of 150 px (previewWidth x previewHeight).

webformatURL	

Medium sized image with a maximum width or height of 640 px (webformatWidth x webformatHeight). URL valid for 24 hours.

Replace '_640' in any webformatURL value to access other image sizes:

Replace with '_180' or '_340' to get a 180 or 340 px tall version of the image, respectively. Replace with '_960' to get the image in a maximum dimension of 960 x 720 px.

largeImageURL	Scaled image with a maximum width/height of 1280px.

views	Total number of views.

downloads	Total number of downloads.

likes	Total number of likes.

comments	Total number of comments.

user_id, user	User ID and name of the contributor. Profile URL: https://pixabay.com/users/{ USERNAME }-{ ID }/

userImageURL	Profile picture URL (250 x 250 px).

The following response key/value pairs are only available if your account has been approved for full API access. These URLs give you access to the original images in full resolution and - if available - in vector format:

Response key	Description

fullHDURL	Full HD scaled image with a maximum width/height of 1920px.

imageURL	URL to the original image (imageWidth x imageHeight).

vectorURL	URL to a vector resource if available, else omitted.

Search Videos

GEThttps://pixabay.com/api/videos/

Parameters

key (required)	str	Your API key: **********************************

q	str	A URL encoded search term. If omitted, all videos are returned. This value may not exceed 100 characters.

Example: "yellow+flower"

lang	str	Language code of the language to be searched in.

Accepted values: cs, da, de, en, es, fr, id, it, hu, nl, no, pl, pt, ro, sk, fi, sv, tr, vi, th, bg, ru, el, ja, ko, zh

Default: "en"

id	str	Retrieve individual videos by ID.

video_type	str	Filter results by video type.

Accepted values: "all", "film", "animation"

Default: "all"

category	str	Filter results by category.

Accepted values: backgrounds, fashion, nature, science, education, feelings, health, people, religion, places, animals, industry, computer, food, sports, transportation, travel, buildings, business, music

min_width	int	Minimum video width.

Default: "0"

min_height	int	Minimum video height.

Default: "0"

editors_choice	bool	Select videos that have received an Editor's Choice award.

Accepted values: "true", "false"

Default: "false"

safesearch	bool	A flag indicating that only videos suitable for all ages should be returned.

Accepted values: "true", "false"

Default: "false"

order	str	How the results should be ordered.

Accepted values: "popular", "latest"

Default: "popular"

page	int	Returned search results are paginated. Use this parameter to select the page number.

Default: 1

per_page	int	Determine the number of results per page.

Accepted values: 3 - 200

Default: 20

callback	string	JSONP callback function name

pretty	bool	Indent JSON output. This option should not be used in production.

Accepted values: "true", "false"

Default: "false"

Example

Retrieving videos about "yellow flowers". The search term q needs to be URL encoded.

https://pixabay.com/api/videos/?key=**********************************&q=yellow+flowers

Response for this request:

{

"total": 42,

"totalHits": 42,

"hits": [

    {

        "id": 125,

        "pageURL": "https://pixabay.com/videos/id-125/",

        "type": "film",

        "tags": "flowers, yellow, blossom",

        "duration": 12,

        "videos": {

            "large": {

                "url": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_large.mp4",

                "width": 1920,

                "height": 1080,

                "size": 6615235,

                "thumbnail": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_large.jpg"

            },

            "medium": {

                "url": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_medium.mp4",

                "width": 1280,

                "height": 720,

                "size": 3562083,

                "thumbnail": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_medium.jpg"

            },

            "small": {

                "url": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_small.mp4",

                "width": 640,

                "height": 360,

                "size": 1030736,

                "thumbnail": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_small.jpg"

            },

            "tiny": {

                "url": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_tiny.mp4",

                "width": 480,

                "height": 270,

                "size": 426799,

                "thumbnail": "https://cdn.pixabay.com/video/2015/08/08/125-135736646_tiny.jpg"

            }

        },

        "views": 4462,

        "downloads": 1464,

        "likes": 18,

        "comments": 0,

        "user_id": 1281706,

        "user": "Coverr-Free-Footage",

        "userImageURL": "https://cdn.pixabay.com/user/2015/10/16/09-28-45-303_250x250.png"

    },

    {

        "id": 473,

        ...

    },

    ...

]

}

Response key	Description

total	The total number of hits.

totalHits	The number of videos accessible through the API. By default, the API is limited to return a maximum of 500 videos per query.

id	A unique identifier for this video.

pageURL	Source page on Pixabay.

videos	

A set of differently sizes video streams:

large usually has a dimension of 3840x2160. If a large video version is not available, an empty URL value and a size of zero is returned.

medium usually has a dimension of 1920x1080, older videos have a dimension of 1280x720. This size is available for all Pixabay videos.

small typically has a dimension of 1280x720, older videos have a dimension of 960x540. This size is available for all videos.

tiny typically has a dimension of 960x540, older videos have a dimension of 640x360. This size is available for all videos.

Object key	Description

url	The video URL. Append the GET parameter download=1 to the value to have the browser download it.

width	The width of the video and thumbnail.

height	The height of the video and thumbnail.

size	The approximate size of the video in bytes.

thumbnail	The URL of the poster image for this rendition.

views	Total number of views.

downloads	Total number of downloads.

likes	Total number of likes.

comments	Total number of comments.

user_id, user	User ID and name of the contributor. Profile URL: https://pixabay.com/users/{ USERNAME }-{ ID }/

userImageURL	Profile picture URL (250 x 250 px).

JavaScript Example

var API_KEY = '**********************************';

var URL = "https://pixabay.com/api/?key="+API_KEY+"&q="+encodeURIComponent('red roses');

$.getJSON(URL, function(data){

if (parseInt(data.totalHits) > 0)

    $.each(data.hits, function(i, hit){ console.log(hit.pageURL); });

else

    console.log('No hits');

});

Support

Request full API access for retrieving high resolution images.

Contact us if you have any questions about the API.

▲

This site is protected by reCAPTCHA and the Google Privacy Policy and Terms of Service apply.

<pixabay documentation> 

<exampleresponse>

{

  "id": "01172233-8018-0139-0000-1c08b9aab903",

  "status_code": 20000,

  "status_message": "Ok.",

  "time": "7.0304 sec.",

  "cost": 0.002,

  "result_count": 1,

  "path": [

    "v3",

    "serp",

    "google",

    "maps",

    "live",

    "advanced"

  ],

  "data": {

    "api": "serp",

    "function": "live",

    "se": "google",

    "se_type": "maps",

    "keyword": "solar panels sligo",

    "location_code": 2372,

    "language_code": "en",

    "device": "desktop",

    "os": "windows",

    "depth": 10

  },

  "result": [

    {

      "keyword": "solar panels sligo",

      "type": "maps",

      "se_domain": "google.ie",

      "location_code": 2372,

      "language_code": "en",

      "check_url": "https://google.ie/maps/search/solar+panels+sligo/@53.7797554,-7.3055309,7z?hl=en&gl=IE&uule=w+CAIQIFISCfsnQFzkullIEQj02840EnzP",

      "datetime": "2025-01-17 20:33:15 +00:00",

      "spell": null,

      "refinement_chips": null,

      "item_types": [

        "maps_search"

      ],

      "se_results_count": 0,

      "items_count": 10,

      "items": [

        {

          "type": "maps_search",

          "rank_group": 1,

          "rank_absolute": 1,

          "domain": "www.solargeneration.ie",

          "title": "Solar Generation",

          "url": "http://www.solargeneration.ie/",

          "contact_url": null,

          "contributor_url": "https://maps.google.com/maps/contrib/101910556177641395664",

          "book_online_url": null,

          "rating": {

            "rating_type": "Max5",

            "value": 5,

            "votes_count": 69,

            "rating_max": null

          },

          "hotel_rating": null,

          "price_level": null,

          "rating_distribution": {

            "1": 0,

            "2": 0,

            "3": 0,

            "4": 0,

            "5": 69

          },

          "snippet": "Unit 9, Old Dublin Rd, Business Park, Carraroe, Co. Sligo, F91 PK68",

          "address": "Unit 9, Old Dublin Rd, Business Park, Carraroe, Co. Sligo, F91 PK68",

          "address_info": {

            "borough": "Business Park",

            "address": "Unit 9, Old Dublin Rd",

            "city": "Carraroe",

            "zip": "F91 PK68",

            "region": "Co. Sligo",

            "country_code": "IE"

          },

          "place_id": "ChIJIegmGqPDXkgRaQVH4DzZmGA",

          "phone": "+353719310111",

          "main_image": "https://lh5.googleusercontent.com/p/AF1QipP__D6Fn9BpBo69VlVIa7n-nuGqKyagn3fIwTES=w408-h305-k-no",

          "total_photos": 46,

          "category": "Solar energy company",

          "additional_categories": null,

          "category_ids": [

            "solar_energy_company"

          ],

          "work_hours": {

            "timetable": {

              "sunday": null,

              "monday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 17,

                    "minute": 30

                  }

                }

              ],

              "tuesday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 17,

                    "minute": 30

                  }

                }

              ],

              "wednesday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 17,

                    "minute": 30

                  }

                }

              ],

              "thursday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 17,

                    "minute": 30

                  }

                }

              ],

              "friday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 17,

                    "minute": 30

                  }

                }

              ],

              "saturday": null

            },

            "current_status": "close"

          },

          "feature_id": "0x485ec3a31a26e821:0x6098d93ce0470569",

          "cid": "6960552079585117545",

          "latitude": 54.228381899999995,

          "longitude": -8.4942832,

          "is_claimed": true,

          "local_justifications": [

            {

              "type": "user_review",

              "text": "\"The installation was professional, tidy, quick and efficient.\""

            }

          ],

          "is_directory_item": false

        },

        {

          "type": "maps_search",

          "rank_group": 2,

          "rank_absolute": 2,

          "domain": "solektric.ie",

          "title": "Solektric",

          "url": "http://solektric.ie/",

          "contact_url": "http://solektric.ie/contact-us",

          "contributor_url": "https://maps.google.com/maps/contrib/108917929604971912907",

          "book_online_url": null,

          "rating": {

            "rating_type": "Max5",

            "value": 5,

            "votes_count": 7,

            "rating_max": null

          },

          "hotel_rating": null,

          "price_level": null,

          "rating_distribution": {

            "1": 0,

            "2": 0,

            "3": 0,

            "4": 0,

            "5": 7

          },

          "snippet": "Carrowgobbadagh, Carraroe, Co. Sligo, F91 K0D8",

          "address": "Carrowgobbadagh, Carraroe, Co. Sligo, F91 K0D8",

          "address_info": {

            "borough": "Carrowgobbadagh",

            "address": null,

            "city": "Carraroe",

            "zip": "F91 K0D8",

            "region": "Co. Sligo",

            "country_code": "IE"

          },

          "place_id": "ChIJz9ohQpvDXkgR-TafEs4o8QI",

          "phone": "+353871891918",

          "main_image": "https://lh5.googleusercontent.com/p/AF1QipPDhFJCzeqC3yAH7f-9E25R8u2426vdNhPO7qdY=w408-h265-k-no",

          "total_photos": 20,

          "category": "Solar energy company",

          "additional_categories": [

            "Electrician"

          ],

          "category_ids": [

            "solar_energy_company",

            "electrician"

          ],

          "work_hours": {

            "timetable": {

              "sunday": null,

              "monday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 18,

                    "minute": 0

                  }

                }

              ],

              "tuesday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 18,

                    "minute": 0

                  }

                }

              ],

              "wednesday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 18,

                    "minute": 0

                  }

                }

              ],

              "thursday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 18,

                    "minute": 0

                  }

                }

              ],

              "friday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 18,

                    "minute": 0

                  }

                }

              ],

              "saturday": [

                {

                  "open": {

                    "hour": 9,

                    "minute": 0

                  },

                  "close": {

                    "hour": 18,

                    "minute": 0

                  }

                }

              ]

            },

            "current_status": "close"

          },

          "feature_id": "0x485ec39b4221dacf:0x2f128ce129f36f9",

          "cid": "211995523003922169",

          "latitude": 54.222009,

          "longitude": -8.502048,

          "is_claimed": true,

          "local_justifications": [

            {

              "type": "user_review",

              "text": "\"We are very satisfied with the service and the solar panels.\""

            }

          ],

          "is_directory_item": false

        },

        {

          "type": "maps_search",

          "rank_group": 3,

          "rank_absolute": 3,

          "domain": null,

          "title": "Solar Energy Ireland",

          "url": null,

          "contact_url": null,

          "contributor_url": null,

          "book_online_url": null,

          "rating": {

            "rating_type": "Max5",

            "value": 3,

            "votes_count": 2,

            "rating_max": null

          },

          "hotel_rating": null,

          "price_level": null,

          "rating_distribution": {

            "1": 1,

            "2": 0,

            "3": 0,

            "4": 0,

            "5": 1

          },

</exampleresponse>


